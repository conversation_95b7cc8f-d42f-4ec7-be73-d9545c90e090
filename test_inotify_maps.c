#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/inotify.h>
#include <sys/select.h>
#include <fcntl.h>

#define TFD_LOGI(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)

// 检查maps文件中的关键字
static int check_maps_keywords(const char *filename) {
    FILE *fp;
    char line[1024];
    
    fp = fopen(filename, "r");
    if (fp == NULL) {
        TFD_LOGI("Failed to open maps file: %s", filename);
        return 0;
    }
    
    while (fgets(line, sizeof(line), fp)) {
        if (strstr(line, "frida-agent") || 
            strstr(line, "frida-agent-32") || 
            strstr(line, "frida-agent-64") ||
            strstr(line, "frida") ||
            strstr(line, "gum-js-loop") ||
            strstr(line, "gmain")) {
            TFD_LOGI("Frida agent detected in maps: %s", line);
            fclose(fp);
            return 1;
        }
    }
    fclose(fp);
    return 0;
}

void test_inotify_maps(){
    int pid = getpid();
    char filename[64];
    int inotify_fd, watch_fd;
    char buffer[4096];
    struct inotify_event *event;
    fd_set readfds;
    struct timeval timeout;
    int ret;

    snprintf(filename, sizeof(filename), "/proc/%d/maps", pid);

    TFD_LOGI("Starting inotify-based frida maps detection: %s", filename);

    // 初始检查一次
    if (check_maps_keywords(filename)) {
        TFD_LOGI("Frida detected during initial check");
        return;
    }

    // 创建inotify实例
    inotify_fd = inotify_init();
    if (inotify_fd < 0) {
        TFD_LOGI("Failed to initialize inotify");
        return;
    }

    // 监控maps文件的修改
    watch_fd = inotify_add_watch(inotify_fd, filename, IN_MODIFY | IN_ATTRIB);
    if (watch_fd < 0) {
        TFD_LOGI("Failed to add watch for maps file");
        close(inotify_fd);
        return;
    }

    TFD_LOGI("Successfully set up inotify watch for maps file");
    TFD_LOGI("Test will run for 30 seconds...");

    int test_duration = 30; // 测试30秒
    time_t start_time = time(NULL);

    while (time(NULL) - start_time < test_duration) {
        FD_ZERO(&readfds);
        FD_SET(inotify_fd, &readfds);
        
        // 设置超时时间
        timeout.tv_sec = 2;
        timeout.tv_usec = 0;
        
        ret = select(inotify_fd + 1, &readfds, NULL, NULL, &timeout);
        
        if (ret < 0) {
            TFD_LOGI("select() error in inotify monitoring");
            break;
        } else if (ret == 0) {
            // 超时，进行一次主动检查
            TFD_LOGI("Timeout - performing active check");
            if (check_maps_keywords(filename)) {
                TFD_LOGI("Frida detected during timeout check");
                break;
            }
            continue;
        }
        
        if (FD_ISSET(inotify_fd, &readfds)) {
            ssize_t length = read(inotify_fd, buffer, sizeof(buffer));
            if (length < 0) {
                TFD_LOGI("Failed to read inotify events");
                continue;
            }
            
            // 处理inotify事件
            for (char *ptr = buffer; ptr < buffer + length; ) {
                event = (struct inotify_event *)ptr;
                
                if (event->mask & (IN_MODIFY | IN_ATTRIB)) {
                    TFD_LOGI("Maps file changed, checking for frida keywords");
                    if (check_maps_keywords(filename)) {
                        TFD_LOGI("Frida detected after maps change");
                        inotify_rm_watch(inotify_fd, watch_fd);
                        close(inotify_fd);
                        return;
                    }
                }
                
                ptr += sizeof(struct inotify_event) + event->len;
            }
        }
    }
    
    TFD_LOGI("Test completed - no frida detected");
    
    // 清理资源
    inotify_rm_watch(inotify_fd, watch_fd);
    close(inotify_fd);
}

int main() {
    TFD_LOGI("Testing inotify-based maps monitoring...");
    test_inotify_maps();
    return 0;
}
